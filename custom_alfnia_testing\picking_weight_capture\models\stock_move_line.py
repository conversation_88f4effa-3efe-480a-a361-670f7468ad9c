# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class StockMoveLine(models.Model):
    _inherit = 'stock.move.line'

    # Field to track if weight has been captured for this line
    weight_captured = fields.Boolean(
        string='Weight Captured',
        default=False,
        help='Indicates if weight has been captured from scale for this line'
    )
    
    # Field to store the captured weight value
    captured_weight = fields.Float(
        string='Captured Weight',
        digits=(16, 3),
        help='Weight value captured from scale'
    )
    
    # Field to store when weight was captured
    weight_capture_time = fields.Datetime(
        string='Weight Capture Time',
        help='Timestamp when weight was captured'
    )
    
    # Field to show/hide capture weight button
    show_capture_weight_button = fields.Bo<PERSON>an(
        string='Show Capture Weight Button',
        compute='_compute_show_capture_weight_button',
        help='Show capture weight button only for outgoing pickings'
    )

    @api.depends('picking_id.picking_type_id.code', 'state')
    def _compute_show_capture_weight_button(self):
        """Show capture weight button only for outgoing pickings that are not done"""
        for line in self:
            line.show_capture_weight_button = (
                line.picking_id and 
                line.picking_id.picking_type_id.code == 'outgoing' and
                line.state not in ('done', 'cancel')
            )

    @api.depends('weight_captured')
    def _compute_quantity_readonly(self):
        """Make quantity_done readonly if weight has been captured"""
        for line in self:
            # Call parent method first if it exists
            if hasattr(super(), '_compute_quantity_readonly'):
                super()._compute_quantity_readonly()
            
            # Make readonly if weight captured
            if line.weight_captured:
                line.quantity_readonly = True

    def action_capture_weight(self):
        """Capture weight from scale and update quantity_done"""
        self.ensure_one()
        
        # Check if this is an outgoing picking
        if self.picking_id.picking_type_id.code != 'outgoing':
            raise UserError(_('Weight capture is only available for outgoing/delivery pickings.'))
        
        # Check if weight already captured
        if self.weight_captured:
            raise UserError(_('Weight has already been captured for this line. Weight: %.3f') % self.captured_weight)
        
        try:
            # Get the first available scale integration (same pattern as truck_weighing)
            scale = self.env['scale.integration'].search([('gateway_connected', '=', True)], limit=1)
            if not scale:
                scale = self.env['scale.integration'].search([], limit=1)

            if not scale:
                raise UserError(_('No scale configuration found. Please configure scale integration first.'))

            # Get weight from scale
            weight_data = scale.get_weight()
            
            if not weight_data:
                raise UserError(_('Failed to get weight from scale. Please check scale connection.'))

            captured_weight = weight_data.get('weight', 0.0)
            
            if captured_weight <= 0:
                raise UserError(_('Invalid weight reading: %.3f. Please ensure product is on scale.') % captured_weight)

            # Update the move line with captured weight
            self.write({
                'quantity': captured_weight,
                'weight_captured': True,
                'captured_weight': captured_weight,
                'weight_capture_time': fields.Datetime.now(),
            })

            # Show success message
            message = _(
                'Weight captured successfully!\n\n'
                'Product: %(product)s\n'
                'Weight: %(weight).3f %(unit)s\n'
                'Status: %(status)s\n'
                'Time: %(time)s'
            ) % {
                'product': self.product_id.display_name,
                'weight': captured_weight,
                'unit': weight_data.get('unit', 'kg'),
                'status': 'Stable' if weight_data.get('stable') else 'Unstable',
                'time': fields.Datetime.now().strftime('%H:%M:%S'),
            }

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Weight Captured'),
                    'message': message,
                    'type': 'success',
                    'sticky': False,
                }
            }

        except Exception as e:
            _logger.error(f"Error capturing weight for move line {self.id}: {e}")
            raise UserError(_('Error capturing weight: %s') % str(e))

    @api.model
    def create(self, vals):
        """Override create to ensure weight_captured is False by default"""
        vals.setdefault('weight_captured', False)
        return super().create(vals)

    def write(self, vals):
        """Override write to prevent manual editing of quantity when weight is captured"""
        # Check if trying to manually edit quantity when weight is captured
        if 'quantity' in vals:
            for line in self:
                if line.weight_captured and not self.env.context.get('allow_weight_edit'):
                    # Allow the weight capture process itself to update quantity
                    if not vals.get('weight_captured') and not vals.get('captured_weight'):
                        raise UserError(_(
                            'Cannot manually edit quantity for line "%(product)s" because weight has been captured from scale. '
                            'Captured weight: %(weight).3f at %(time)s'
                        ) % {
                            'product': line.product_id.display_name,
                            'weight': line.captured_weight,
                            'time': line.weight_capture_time.strftime('%Y-%m-%d %H:%M:%S') if line.weight_capture_time else 'Unknown'
                        })
        
        return super().write(vals)
