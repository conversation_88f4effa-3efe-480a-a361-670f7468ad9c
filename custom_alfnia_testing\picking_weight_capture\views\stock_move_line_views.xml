<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Inherit stock move line tree view to add capture weight button -->
        <record id="view_stock_move_line_tree_weight_capture" model="ir.ui.view">
            <field name="name">stock.move.line.tree.weight.capture</field>
            <field name="model">stock.move.line</field>
            <field name="inherit_id" ref="stock.view_stock_move_line_operation_tree"/>
            <field name="arch" type="xml">
                <!-- Add capture weight button before quantity field -->
                <xpath expr="//field[@name='quantity']" position="before">
                    <button name="action_capture_weight" 
                            type="object"
                            string="📏 Capture Weight" 
                            class="btn-primary"
                            title="Capture weight from scale"
                            invisible="not show_capture_weight_button or weight_captured"/>
                    <field name="show_capture_weight_button" invisible="1"/>
                    <field name="weight_captured" invisible="1"/>
                </xpath>
                
                <!-- Make quantity field readonly when weight is captured -->
                <xpath expr="//field[@name='quantity']" position="attributes">
                    <attribute name="readonly">weight_captured</attribute>
                </xpath>
                

            </field>
        </record>

        <!-- Inherit stock picking form view to add weight capture info -->
        <record id="view_picking_form_weight_capture" model="ir.ui.view">
            <field name="name">stock.picking.form.weight.capture</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_form"/>
            <field name="arch" type="xml">
                <!-- Add weight capture instructions to operations page -->
                <xpath expr="//page[@name='operations']" position="inside">
                    <div class="alert alert-info" invisible="picking_type_code != 'outgoing'">
                        <p><strong>Weight Capture:</strong> Use the "Detailed Operations" button above to access individual line weight capture functionality.</p>
                    </div>
                </xpath>
            </field>
        </record>

        <!-- Inherit detailed operations view -->
        <record id="view_stock_move_line_detailed_operation_tree_weight_capture" model="ir.ui.view">
            <field name="name">stock.move.line.detailed.operation.tree.weight.capture</field>
            <field name="model">stock.move.line</field>
            <field name="inherit_id" ref="stock.view_stock_move_line_detailed_operation_tree"/>
            <field name="arch" type="xml">
                <!-- Add capture weight button in detailed operations -->
                <xpath expr="//field[@name='quantity']" position="before">
                    <button name="action_capture_weight" 
                            type="object"
                            string="📏"
                            class="btn-primary btn-sm"
                            title="Capture Weight from Scale"
                            invisible="not show_capture_weight_button or weight_captured"/>
                    <field name="show_capture_weight_button" invisible="1"/>
                    <field name="weight_captured" invisible="1"/>
                </xpath>
                
                <!-- Make quantity readonly when weight captured -->
                <xpath expr="//field[@name='quantity']" position="attributes">
                    <attribute name="readonly">weight_captured</attribute>
                </xpath>
                
                <!-- Add captured weight info -->
                <xpath expr="//field[@name='quantity']" position="after">
                    <field name="captured_weight" 
                           invisible="not weight_captured"
                           readonly="1"
                           string="Captured"/>
                </xpath>
            </field>
        </record>



    </data>
</odoo>
