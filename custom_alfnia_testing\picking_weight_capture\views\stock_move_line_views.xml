<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Inherit stock move line tree view to add capture weight button -->
        <record id="view_stock_move_line_tree_weight_capture" model="ir.ui.view">
            <field name="name">stock.move.line.tree.weight.capture</field>
            <field name="model">stock.move.line</field>
            <field name="inherit_id" ref="stock.view_stock_move_line_operation_tree"/>
            <field name="arch" type="xml">
                <!-- Add capture weight button before quantity field -->
                <xpath expr="//field[@name='quantity']" position="before">
                    <button name="action_capture_weight" 
                            type="object"
                            string="📏 Capture Weight" 
                            class="btn-primary"
                            title="Capture weight from scale"
                            invisible="not show_capture_weight_button or weight_captured"/>
                    <field name="show_capture_weight_button" invisible="1"/>
                    <field name="weight_captured" invisible="1"/>
                </xpath>
                
                <!-- Make quantity field readonly when weight is captured -->
                <xpath expr="//field[@name='quantity']" position="attributes">
                    <attribute name="readonly">weight_captured</attribute>
                </xpath>
                
                <!-- Add weight capture info fields -->
                <xpath expr="//field[@name='quantity']" position="after">
                    <field name="captured_weight" 
                           string="Captured Weight"
                           invisible="not weight_captured"
                           readonly="1"/>
                    <field name="weight_capture_time" 
                           string="Capture Time"
                           invisible="not weight_captured"
                           readonly="1"/>
                </xpath>
            </field>
        </record>

        <!-- Inherit stock picking form view to modify move lines -->
        <record id="view_picking_form_weight_capture" model="ir.ui.view">
            <field name="name">stock.picking.form.weight.capture</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_form"/>
            <field name="arch" type="xml">
                <!-- Modify move_line_ids_without_package tree view -->
                <xpath expr="//field[@name='move_line_ids_without_package']/tree" position="inside">
                    <!-- Add capture weight button -->
                    <button name="action_capture_weight" 
                            type="object"
                            string="📏"
                            class="btn-primary btn-sm"
                            title="Capture Weight"
                            invisible="not show_capture_weight_button or weight_captured"/>
                    
                    <!-- Add hidden fields for visibility control -->
                    <field name="show_capture_weight_button" invisible="1"/>
                    <field name="weight_captured" invisible="1"/>
                    <field name="captured_weight" invisible="1"/>
                    <field name="weight_capture_time" invisible="1"/>
                </xpath>
                
                <!-- Make quantity field readonly when weight is captured in the form -->
                <xpath expr="//field[@name='move_line_ids_without_package']/tree/field[@name='quantity']" position="attributes">
                    <attribute name="readonly">weight_captured</attribute>
                </xpath>
            </field>
        </record>

        <!-- Inherit detailed operations view -->
        <record id="view_stock_move_line_detailed_operation_tree_weight_capture" model="ir.ui.view">
            <field name="name">stock.move.line.detailed.operation.tree.weight.capture</field>
            <field name="model">stock.move.line</field>
            <field name="inherit_id" ref="stock.view_stock_move_line_detailed_operation_tree"/>
            <field name="arch" type="xml">
                <!-- Add capture weight button in detailed operations -->
                <xpath expr="//field[@name='quantity']" position="before">
                    <button name="action_capture_weight" 
                            type="object"
                            string="📏"
                            class="btn-primary btn-sm"
                            title="Capture Weight from Scale"
                            invisible="not show_capture_weight_button or weight_captured"/>
                    <field name="show_capture_weight_button" invisible="1"/>
                    <field name="weight_captured" invisible="1"/>
                </xpath>
                
                <!-- Make quantity readonly when weight captured -->
                <xpath expr="//field[@name='quantity']" position="attributes">
                    <attribute name="readonly">weight_captured</attribute>
                </xpath>
                
                <!-- Add captured weight info -->
                <xpath expr="//field[@name='quantity']" position="after">
                    <field name="captured_weight" 
                           invisible="not weight_captured"
                           readonly="1"
                           string="Captured"/>
                </xpath>
            </field>
        </record>

        <!-- Form view for individual stock move line -->
        <record id="view_stock_move_line_form_weight_capture" model="ir.ui.view">
            <field name="name">stock.move.line.form.weight.capture</field>
            <field name="model">stock.move.line</field>
            <field name="inherit_id" ref="stock.stock_move_line_view_form"/>
            <field name="arch" type="xml">
                <!-- Add weight capture section in form view -->
                <xpath expr="//field[@name='quantity']" position="after">
                    <group string="Weight Capture" invisible="not show_capture_weight_button">
                        <field name="show_capture_weight_button" invisible="1"/>
                        <field name="weight_captured"/>
                        <field name="captured_weight" readonly="1" invisible="not weight_captured"/>
                        <field name="weight_capture_time" readonly="1" invisible="not weight_captured"/>
                        <button name="action_capture_weight" 
                                type="object"
                                string="📏 Capture Weight from Scale" 
                                class="btn-primary"
                                invisible="weight_captured"/>
                    </group>
                </xpath>
                
                <!-- Make quantity readonly when weight captured -->
                <xpath expr="//field[@name='quantity']" position="attributes">
                    <attribute name="readonly">weight_captured</attribute>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
