# -*- coding: utf-8 -*-
{
    'name': 'Picking Weight Capture',
    'version': '********.0',
    'category': 'Inventory/Inventory',
    'summary': 'Weight capture for delivery picking operations using scale integration',
    'description': """
Picking Weight Capture
======================

This module provides weight capture functionality for delivery/outgoing picking operations.

Features:
---------
* Weight capture button on each stock move line for outgoing pickings
* Integration with scale_integration module for real-time weight reading
* Automatic update of quantity_done field with captured weight
* Readonly quantity_done field once weight is captured
* Only applies to delivery/outgoing picking operations

Dependencies:
-------------
* Requires scale_integration module to be installed and configured
* Uses the same scale communication pattern as truck_weighing module

Usage:
------
1. Open any outgoing/delivery picking
2. Click "Capture Weight" button on individual move lines
3. Weight will be automatically captured from scale and set as quantity_done
4. quantity_done becomes readonly after weight capture
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'depends': [
        'stock',
        'scale_integration',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/stock_move_line_views.xml',
    ],
    'demo': [],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}
